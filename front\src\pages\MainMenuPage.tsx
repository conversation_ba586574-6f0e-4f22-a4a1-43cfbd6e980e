import React from 'react'
import { useNavigate } from 'react-router-dom'
import MainMenu from '../components/MainMenu'
import { useGameStore } from '../store/gameStore'
import { WorldMap } from '../shared/types/World'
import { Language } from '../shared/enums'

const MainMenuPage: React.FC = () => {
  const navigate = useNavigate()
  const { setCurrentWorld } = useGameStore()

  const handleStartGame = (worldId: string) => {
    // Создаем временный объект WorldMap
    // TODO: В будущем здесь должна быть загрузка полного мира из API
    const mockWorldMap: WorldMap = {
      id: worldId,
      userId: '',
      name: 'Загружаемый мир',
      description: 'Мир загружается...',
      parameters: {
        currentTime: {
          day: 1,
          hour: 12,
          minute: 0,
          season: 'spring'
        },
        weather: {
          temperature: 20,
          humidity: 50,
          windSpeed: 5,
          precipitation: 0,
          visibility: 100,
          radiationStorm: false
        },
        activeEvents: []
      },
      settings: {
        seed: 'default-seed',
        language: Language.RU,
        worldSize: 100,
        difficulty: 'normal',
        autosave: 'when_rest',
        timeScale: 0.5
      },
      worldMap: {},
      createdAt: new Date(),
      updatedAt: new Date()
    }

    setCurrentWorld(mockWorldMap)
    navigate('/game')
  }

  return <MainMenu onStartGame={handleStartGame} />
}

export default MainMenuPage
