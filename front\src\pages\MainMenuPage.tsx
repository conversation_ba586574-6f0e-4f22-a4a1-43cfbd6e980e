import React from 'react'
import { useNavigate } from 'react-router-dom'
import MainMenu from '../components/MainMenu'
import { useGameStore } from '../store/gameStore'

const MainMenuPage: React.FC = () => {
  const navigate = useNavigate()
  const { setCurrentWorld } = useGameStore()

  const handleStartGame = (worldId: string) => {
    // TODO: Здесь нужно загрузить полный объект мира (WorldMap) из API
    // Пока что устанавливаем null, так как setCurrentWorld теперь принимает WorldMap
    setCurrentWorld(null)
    navigate('/game')
  }

  return <MainMenu onStartGame={handleStartGame} />
}

export default MainMenuPage
