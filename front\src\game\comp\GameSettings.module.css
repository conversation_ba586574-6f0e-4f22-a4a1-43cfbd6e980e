/* Стили для компонента настроек игры */

.gameSettings {
  background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
  border: 2px solid #444;
  border-radius: 12px;
  width: 100%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  color: #e0e0e0;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.7);
}

.header {
  padding: 20px;
  border-bottom: 1px solid #444;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(0, 0, 0, 0.3);
}

.header h2 {
  margin: 0;
  color: #ff6b35;
  font-size: 22px;
  text-shadow: 0 0 10px rgba(255, 107, 53, 0.3);
}

.closeButton {
  background: transparent;
  border: 1px solid #666;
  color: #ccc;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: #888;
}

.content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.section {
  margin-bottom: 30px;
}

.section:last-child {
  margin-bottom: 0;
}

.sectionTitle {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  color: #ff6b35;
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 0 5px rgba(255, 107, 53, 0.3);
}

.setting {
  margin-bottom: 16px;
}

.settingLabel {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  color: #ccc;
  font-size: 14px;
  font-weight: 500;
}

.slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #444;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #ff6b35;
  cursor: pointer;
  box-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
  transition: all 0.3s ease;
}

.slider::-webkit-slider-thumb:hover {
  background: #e55a2b;
  box-shadow: 0 0 15px rgba(255, 107, 53, 0.7);
  transform: scale(1.1);
}

.slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #ff6b35;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
  transition: all 0.3s ease;
}

.slider::-moz-range-thumb:hover {
  background: #e55a2b;
  box-shadow: 0 0 15px rgba(255, 107, 53, 0.7);
  transform: scale(1.1);
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  color: #ccc;
  font-size: 14px;
  transition: color 0.3s ease;
}

.checkboxLabel:hover {
  color: #ff6b35;
}

.checkboxLabel input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #ff6b35;
  cursor: pointer;
}

.select {
  width: 100%;
  padding: 8px 12px;
  background: #333;
  border: 1px solid #555;
  border-radius: 6px;
  color: #e0e0e0;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.select:focus {
  outline: none;
  border-color: #ff6b35;
  box-shadow: 0 0 10px rgba(255, 107, 53, 0.3);
}

.select option {
  background: #333;
  color: #e0e0e0;
}

.footer {
  padding: 20px;
  border-top: 1px solid #444;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(0, 0, 0, 0.3);
}

.resetButton {
  background: transparent;
  border: 1px solid #666;
  color: #ccc;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.resetButton:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: #888;
  color: #fff;
}

.actionButtons {
  display: flex;
  gap: 12px;
}

.cancelButton {
  background: transparent;
  border: 1px solid #666;
  color: #ccc;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.cancelButton:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: #888;
  color: #fff;
}

.saveButton {
  background: #ff6b35;
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.saveButton:hover {
  background: #e55a2b;
  box-shadow: 0 0 15px rgba(255, 107, 53, 0.4);
  transform: translateY(-1px);
}

.saveButton:active {
  transform: translateY(0);
}

/* Скроллбар для контента */
.content::-webkit-scrollbar {
  width: 8px;
}

.content::-webkit-scrollbar-track {
  background: #333;
  border-radius: 4px;
}

.content::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.content::-webkit-scrollbar-thumb:hover {
  background: #666;
}

/* Адаптивность */
@media (max-width: 768px) {
  .gameSettings {
    max-width: 95%;
    margin: 10px;
  }
  
  .header {
    padding: 15px;
  }
  
  .content {
    padding: 15px;
  }
  
  .footer {
    padding: 15px;
    flex-direction: column;
    gap: 15px;
  }
  
  .actionButtons {
    width: 100%;
    justify-content: center;
  }
  
  .resetButton {
    align-self: flex-start;
  }
}
