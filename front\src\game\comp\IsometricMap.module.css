.mapContainer {

  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  border-radius: 8px;
  overflow: hidden;
}

.mapCanvas {

  border-radius: 4px;
  background: var(--bg-primary);
  box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.5);
}

.mapCanvas:active {
  cursor: grabbing !important;
}

.mapControls {
  position: absolute;
  top: 10px;
  left: 10px;
  background: var(--bg-overlay);
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  backdrop-filter: blur(5px);
}

.coordinates {
  color: var(--primary-color);
  font-size: 12px;
  font-family: monospace;
  margin-bottom: 4px;
}

.instructions {
  color: var(--text-muted);
  font-size: 11px;
  max-width: 200px;
}
