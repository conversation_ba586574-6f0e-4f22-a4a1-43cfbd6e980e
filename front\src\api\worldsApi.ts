import { AuthApiClient } from '../utils/authFetch';
import { WorldSettings, WorldSummary } from '../shared/types/World';

const GENERATOR_SERVICE_URL = '/api';
const SAVE_SERVICE_URL = '/api';

const generatorClient = new AuthApiClient(GENERATOR_SERVICE_URL);
const saveClient = new AuthApiClient(SAVE_SERVICE_URL);

export async function createWorld(data: {
  name: string;
  description: string;
  userId: string;
  settings?: WorldSettings;
}): Promise<{ success: boolean; world: any }> {
  try {
    const result = await generatorClient.post<{ success: boolean; world: any }>('/generate-world', data);
    return result;
  } catch (error) {
    throw error;
  }
}

export async function getUserWorlds(userId: string): Promise<{ worlds: WorldSummary[] }> {
  const { token, isAuthenticated } = await import('../store/authStore').then((m) => m.useAuthStore.getState());
  try {
    const data = await saveClient.get<{ success: boolean; worlds: WorldSummary[] }>(`/worlds?userId=${userId}`);
    if (data.success && data.worlds) {
      return { worlds: data.worlds };
    } else {
      return { worlds: [] };
    }
  } catch (err) {
    if (err instanceof Error && err.message.includes('404')) {
      return { worlds: [] };
    }
    throw err;
  }
}

export async function deleteWorld(worldId: string, userId: string): Promise<{ success: boolean }> {
  try {
    const result = await saveClient.delete<{ success: boolean }>(`/worlds/${worldId}?userId=${userId}`);
    return result;
  } catch (error) {
    if (error instanceof Error && error.message.includes('404')) {
      return { success: true };
    }
    throw error;
  }
}
