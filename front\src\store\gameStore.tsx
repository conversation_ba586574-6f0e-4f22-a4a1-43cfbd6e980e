import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { WorldSummary, WorldSettings } from '../shared/types/World'

// Используем типы из shared модуля
export type World = WorldSummary & {
  lastPlayed: Date
  progress: number
}

export type GameSettings = {
  volume: number
  soundEnabled: boolean
  musicEnabled: boolean
  difficulty: WorldSettings['difficulty']
  autoSave: boolean
}

interface GameState {
  worlds: World[]
  settings: GameSettings
  currentWorldId: string | null
  
  // Actions
  addWorld: (world: Omit<World, 'id' | 'createdAt' | 'lastPlayed'>) => void
  deleteWorld: (worldId: string) => void
  updateWorld: (worldId: string, updates: Partial<World>) => void
  setCurrentWorld: (worldId: string | null) => void
  updateSettings: (settings: Partial<GameSettings>) => void
  loadWorld: (worldId: string) => void
  setWorlds: (worlds: World[]) => void
}

const defaultSettings: GameSettings = {
  volume: 0.7,
  soundEnabled: true,
  musicEnabled: true,
  difficulty: 'normal',
  autoSave: true
}

export const useGameStore = create<GameState>()(
  persist(
    (set, get) => ({
      worlds: [],
      settings: defaultSettings,
      currentWorldId: null,

      addWorld: (worldData) => {
        const newWorld: World = {
          ...worldData,
          id: crypto.randomUUID(),
          createdAt: new Date().toISOString(),
          lastPlayed: new Date(),
          progress: 0
        }
        
        set((state) => ({
          worlds: [...state.worlds, newWorld]
        }))
      },

      deleteWorld: (worldId) => {
        set((state) => ({
          worlds: state.worlds.filter(world => world.id !== worldId),
          currentWorldId: state.currentWorldId === worldId ? null : state.currentWorldId
        }))
      },

      updateWorld: (worldId, updates) => {
        set((state) => ({
          worlds: state.worlds.map(world =>
            world.id === worldId
              ? { ...world, ...updates, lastPlayed: new Date() }
              : world
          )
        }))
      },

      setCurrentWorld: (worldId) => {
        set({ currentWorldId: worldId })
      },

      updateSettings: (newSettings) => {
        set((state) => ({
          settings: { ...state.settings, ...newSettings }
        }))
      },

      loadWorld: (worldId) => {
        const { updateWorld, setCurrentWorld } = get()
        updateWorld(worldId, { lastPlayed: new Date() })
        setCurrentWorld(worldId)
      },

      setWorlds: (worlds) => {
        set({ worlds })
      }
    }),
    {
      name: 'nuclear-story-game-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        worlds: state.worlds,
        settings: state.settings,
        currentWorldId: state.currentWorldId
      })
    }
  )
)
