import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { WorldSummary, WorldSettings, WorldMap } from '../shared/types/World'

// Локальные игровые данные, которых нет в shared типах
export interface LocalWorldData {
  lastPlayed: Date
  progress: number
}

// Комбинированный тип для локального использования
export type World = WorldSummary & LocalWorldData

export type GameSettings = {
  volume: number
  soundEnabled: boolean
  musicEnabled: boolean
  difficulty: WorldSettings['difficulty']
  autoSave: boolean
}

interface GameState {
  worlds: World[]
  settings: GameSettings
  currentWorldId: string | null
  currentWorld: WorldMap | null  // Полная копия выбранного мира

  // Actions
  addWorld: (world: Omit<World, 'id' | 'createdAt' | 'lastPlayed'>) => void
  deleteWorld: (worldId: string) => void
  updateWorld: (worldId: string, updates: Partial<World>) => void
  setCurrentWorld: (world: WorldMap | null) => void
  updateSettings: (settings: Partial<GameSettings>) => void
  loadWorld: (worldId: string) => void
  setWorlds: (worlds: World[]) => void
  getCurrentWorld: () => WorldMap | null
}

const defaultSettings: GameSettings = {
  volume: 0.7,
  soundEnabled: true,
  musicEnabled: true,
  difficulty: 'normal',
  autoSave: true
}

export const useGameStore = create<GameState>()(
  persist(
    (set, get) => ({
      worlds: [],
      settings: defaultSettings,
      currentWorldId: null,
      currentWorld: null,

      addWorld: (worldData) => {
        const newWorld: World = {
          ...worldData,
          id: crypto.randomUUID(),
          createdAt: new Date().toISOString(),
          lastPlayed: new Date(),
          progress: 0
        }
        
        set((state) => ({
          worlds: [...state.worlds, newWorld]
        }))
      },

      deleteWorld: (worldId) => {
        set((state) => ({
          worlds: state.worlds.filter(world => world.id !== worldId),
          currentWorldId: state.currentWorldId === worldId ? null : state.currentWorldId,
          currentWorld: state.currentWorld?.id === worldId ? null : state.currentWorld
        }))
      },

      updateWorld: (worldId, updates) => {
        set((state) => ({
          worlds: state.worlds.map(world =>
            world.id === worldId
              ? { ...world, ...updates, lastPlayed: new Date() }
              : world
          )
        }))
      },

      setCurrentWorld: (world) => {
        set({
          currentWorldId: world?.id || null,
          currentWorld: world
        })
      },

      updateSettings: (newSettings) => {
        set((state) => ({
          settings: { ...state.settings, ...newSettings }
        }))
      },

      loadWorld: (worldId) => {
        const { updateWorld } = get()
        updateWorld(worldId, { lastPlayed: new Date() })
        // setCurrentWorld теперь принимает полный объект мира,
        // поэтому loadWorld должен использоваться только для обновления времени последней игры
        // Для установки текущего мира используйте setCurrentWorld напрямую
      },

      setWorlds: (worlds) => {
        set({ worlds })
      },

      getCurrentWorld: () => {
        const { currentWorld } = get()
        return currentWorld
      }
    }),
    {
      name: 'nuclear-story-game-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        worlds: state.worlds,
        settings: state.settings,
        currentWorldId: state.currentWorldId,
        currentWorld: state.currentWorld
      })
    }
  )
)
