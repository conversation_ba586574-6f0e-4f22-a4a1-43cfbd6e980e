import React, { useState, useEffect } from 'react'
import { Plus, Trash2, Play, Calendar, Clock, BarChart3, Save, Languages } from 'lucide-react'
import { useGameStore, World } from '../store/gameStore'
import { useAuthStore } from '../store/authStore'
import { createWorld, getUserWorlds, deleteWorld as deleteWorldApi } from '../api/worldsApi'
import { Language } from '../shared/enums'
import { WorldSettings } from '../shared/types/World'
import styles from './WorldList.module.css'

interface WorldListProps {
  onWorldSelect: (worldId: string) => void
  onCreateFormToggle?: (isVisible: boolean) => void
}

const WorldList: React.FC<WorldListProps> = ({ onWorldSelect }) => {
  const { worlds, addWorld, deleteWorld, loadWorld, setWorlds, setCurrentWorld } = useGameStore()
  const { user, isAuthenticated, token } = useAuthStore()
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [newWorldName, setNewWorldName] = useState('')
  const [newWorldDescription, setNewWorldDescription] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Настройки мира
  const [worldSettings, setWorldSettings] = useState<WorldSettings>({
    difficulty: 'normal',
    autosave: 'when_rest',
    seed: '',
    language: Language.RU,
    worldSize: 100,
    timeScale: 0.5
  })

  // Загружаем миры когда пользователь авторизован
  useEffect(() => {
    if (user?.id) {
      loadWorlds()
    }
  }, [user?.id])

  const loadWorlds = async () => {
    if (!user?.id) {
      return
    }

    if (!isAuthenticated) {
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      const result = await getUserWorlds(user.id)

      // Преобразуем данные из API в формат стора
      const worldsForStore = result.worlds.map(w => ({
        id: w.id,
        name: w.name,
        description: w.description,
        createdAt: w.createdAt,
        lastPlayedAt: w.lastPlayedAt,
        totalPlaytime: w.totalPlaytime,
        lastPlayed: new Date(w.lastPlayedAt || w.createdAt),
        progress: w.totalPlaytime || 0
      }))

      setWorlds(worldsForStore)
    } catch (error) {
      // НЕ ПОКАЗЫВАЕМ НИКАКИХ ОШИБОК ПРИ ЗАГРУЗКЕ МИРОВ
      // setError('Не удалось загрузить миры')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateWorld = async () => {
    if (!newWorldName.trim()) return
    if (!user?.id) {
      setError('Ошибка авторизации')
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      // Генерируем случайный seed если пользователь не указал
      const finalSeed = worldSettings.seed.trim() || Math.random().toString(36).substring(2, 15);

      const createWorldData = {
        name: newWorldName.trim(),
        description: newWorldDescription.trim() || 'Новый мир',
        userId: user.id,
        settings: {
          difficulty: worldSettings.difficulty,
          worldSize: worldSettings.worldSize,
          seed: finalSeed,
          language: worldSettings.language,
          autosave: worldSettings.autosave,
          timeScale: worldSettings.timeScale
        }
      }

      console.log('🚀 Отправляем POST запрос на создание мира:', createWorldData)
      
      const result = await createWorld(createWorldData)
      
      console.log('✅ Получен ответ от сервера:', result)

      if (result.success) {
        // После создания мира — загружаем все миры пользователя
        await loadWorlds()

        setNewWorldName('')
        setNewWorldDescription('')
        setShowCreateForm(false)
      }
    } catch (error) {
      setError('Не удалось создать мир')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteWorld = async (worldId: string, worldName: string) => {
    if (!confirm(`Вы уверены, что хотите удалить мир "${worldName}"? Это действие нельзя отменить.`)) {
      return
    }

    if (!user?.id) {
      setError('Ошибка авторизации')
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      // Пробуем удалить на сервере
      const result = await deleteWorldApi(worldId, user.id)

      // Удаляем из локального стора только если API вернул успех
      if (result.success) {
        deleteWorld(worldId)
      } else {
        setError('Не удалось удалить мир на сервере')
        return
      }

      // И обновляем список с сервера, чтобы синхронизировать
      await loadWorlds()
    } catch (error) {
      setError('Произошла ошибка при удалении мира')
    } finally {
      setIsLoading(false)
    }
  }

  const handleLoadWorld = (worldId: string) => {
    // Устанавливаем текущий мир в store
    setCurrentWorld(worldId)
    // Загружаем данные мира
    loadWorld(worldId)
    // Вызываем callback для перенаправления на игровую страницу
    onWorldSelect(worldId)
  }

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('ru-RU', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className={styles.worldList}>
      {!showCreateForm && (
        <div className={styles.createButton}>
          <button
            onClick={() => setShowCreateForm(true)}
            className={styles.createWorldButton}
            disabled={isLoading}
          >
            <Plus />
        
            {isLoading ? 'Загрузка...' : 'Создать новый мир'}
          </button>
        </div>
      )}

      {/* Create World Form */}
      {showCreateForm && (
        <div className={styles.createForm}>
          <h3 className={styles.formTitle}>Создание нового мира</h3>
          <div className={styles.formFields}>
            {/* Основная информация */}
            <div className={styles.formSection}>
              <h4 className={styles.sectionTitle}>Основная информация</h4>
              <div className={styles.formField}>
                <label className={styles.formLabel}>
                  Название мира *
                </label>
                <input
                  type="text"
                  value={newWorldName}
                  onChange={(e) => setNewWorldName(e.target.value)}
                  placeholder="Введите название мира..."
                  className={styles.formInput}
                  maxLength={50}
                />
              </div>
              <div className={styles.formField}>
                <label className={styles.formLabel}>
                  Описание (необязательно)
                </label>
                <textarea
                  value={newWorldDescription}
                  onChange={(e) => setNewWorldDescription(e.target.value)}
                  placeholder="Краткое описание мира..."
                  className={`${styles.formInput} ${styles.formTextarea}`}
                  rows={3}
                  maxLength={200}
                />
              </div>
            </div>

            {/* Игровые настройки */}
            <div className={styles.formSection}>
              <h4 className={styles.sectionTitle}>🎮 Игровые настройки</h4>

              {/* Сложность */}
              <div className={styles.formField}>
                <label className={styles.formLabel}>Сложность</label>
                <div className={styles.difficultyGrid}>
                  {(['easy', 'normal', 'hard'] as WorldSettings['difficulty'][]).map((diff) => (
                    <button
                      key={diff}
                      onClick={() => setWorldSettings(prev => ({ ...prev, difficulty: diff }))}
                      type="button"
                      className={`${styles.difficultyButton} ${worldSettings.difficulty === diff
                          ? styles.difficultyButtonActive
                          : styles.difficultyButtonInactive
                        }`}
                    >
                      {diff === 'easy' && 'Легко'}
                      {diff === 'normal' && 'Нормально'}
                      {diff === 'hard' && 'Сложно'}
                    </button>
                  ))}
                </div>
                <p className={styles.difficultyDescription}>
                  {worldSettings.difficulty === 'easy' && 'Больше ресурсов, меньше опасностей'}
                  {worldSettings.difficulty === 'normal' && 'Сбалансированный игровой процесс'}
                  {worldSettings.difficulty === 'hard' && 'Ограниченные ресурсы, высокие риски'}
                </p>
              </div>

              {/* Автосохранение */}
              <div className={styles.formField}>
                <label className={styles.formLabel}>Автосохранение</label>
                <select
                  className={styles.formInput}
                  value={worldSettings.autosave}
                  onChange={(e) => setWorldSettings(prev => ({
                    ...prev,
                    autosave: e.target.value as WorldSettings['autosave']
                  }))}
                >
                  <option value="everytime">Каждый раз (частое автосохранение)</option>
                  <option value="when_rest">Во время отдыха</option>
                  <option value="on_exit">Только при выходе из игры</option>
                  <option value="forbidden">Отключено (хардкор режим)</option>
                </select>
                <p className={styles.settingDescription}>
                  {worldSettings.autosave === 'forbidden' && 'Внимание: При смерти персонажа игра будет завершена без возможности загрузки!'}
                </p>
              </div>
            </div>

            {/* Расширенные настройки */}
            <div className={styles.formSection}>
              <h4 className={styles.sectionTitle}>⚙️ Расширенные настройки</h4>

              {/* Язык */}
              <div className={styles.formField}>
                <label className={styles.formLabel}>
                  <Languages className={styles.inputIcon} />
                  Язык мира
                </label>
                <select
                  className={styles.formInput}
                  value={worldSettings.language}
                  onChange={(e) => {
                    const selectedLang = e.target.value as keyof typeof Language;
                    setWorldSettings(prev => ({
                      ...prev,
                      language: Language[selectedLang]
                    }));
                  }}
                >
                  <option value="RU">Русский</option>
                  <option value="EN">Английский</option>
                  <option value="UK">Украинский</option>
                  <option value="PL">Польский</option>
                  <option value="DE">Немецкий</option>
                  <option value="FR">Французский</option>
                  <option value="ES">Испанский</option>
                  <option value="CN">Китайский</option>
                  <option value="JP">Японский</option>
                </select>
              </div>

              {/* Размер мира */}
              <div className={styles.formField}>
                <label className={styles.formLabel}>
                  Размер мира: {worldSettings.worldSize}x{worldSettings.worldSize}
                </label>
                <input
                  type="range"
                  min="50"
                  max="200"
                  step="10"
                  value={worldSettings.worldSize}
                  onChange={(e) => setWorldSettings(prev => ({ ...prev, worldSize: parseInt(e.target.value) }))}
                  className={styles.slider}
                />
                <div className={styles.rangeLabels}>
                  <span>Маленький</span>
                  <span>Средний</span>
                  <span>Большой</span>
                </div>
              </div>

              {/* Масштаб времени */}
              <div className={styles.formField}>
                <label className={styles.formLabel}>
                  Скорость течения времени: {worldSettings.timeScale.toFixed(1)}x
                </label>
                <input
                  type="range"
                  min="0.1"
                  max="1.0"
                  step="0.1"
                  value={worldSettings.timeScale}
                  onChange={(e) => setWorldSettings(prev => ({ ...prev, timeScale: parseFloat(e.target.value) }))}
                  className={styles.slider}
                />
                <div className={styles.rangeLabels}>
                  <span>Медленно</span>
                  <span>Стандартно</span>
                  <span>Быстро</span>
                </div>
              </div>

              {/* Сид генерации */}
              <div className={styles.formField}>
                <label className={styles.formLabel}>
                  Сид генерации (необязательно)
                </label>
                <input
                  type="text"
                  value={worldSettings.seed}
                  onChange={(e) => setWorldSettings(prev => ({ ...prev, seed: e.target.value }))}
                  placeholder="Оставьте пустым для случайной генерации..."
                  className={styles.formInput}
                />
                <p className={styles.settingDescription}>
                  Используйте один и тот же сид для создания идентичных миров
                </p>
              </div>
            </div>

            <div className={styles.formButtons}>
              <button
                onClick={handleCreateWorld}
                disabled={!newWorldName.trim() || isLoading}
                className={`${styles.formButton} ${styles.greenButton}`}
              >
                {isLoading ? 'Создание...' : <><Plus /> Создать мир</>}
              </button>
              <button
                onClick={() => {
                  setShowCreateForm(false)
                  setNewWorldName('')
                  setNewWorldDescription('')
                  // Сбрасываем настройки к значениям по умолчанию
                  setWorldSettings({
                    difficulty: 'normal',
                    autosave: 'when_rest',
                    seed: '',
                    language: Language.RU,
                    worldSize: 100,
                    timeScale: 0.5
                  })
                }}
                className={`${styles.formButton} ${styles.cancelButton}`}
              >
                Отмена
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Worlds List */}
      {!showCreateForm && (
        worlds.length === 0 ? (
          <div className={styles.emptyState}>
            {/* <div className={styles.emptyIcon}>🌍</div> */}
            <h3 className={styles.emptyTitle}>Нет созданных миров</h3>
            <p className={styles.emptyDescription}>
              Создайте свой первый мир, чтобы начать приключение в постапокалиптической пустоши
            </p>
          </div>
        ) : (
          <div className={styles.worldsGrid}>
            {worlds
              .sort((a, b) => new Date(b.lastPlayed).getTime() - new Date(a.lastPlayed).getTime())
              .map((world) => (
                <div
                  key={world.id}
                  className={styles.worldCard}
                >
                  <div className={styles.worldCardContent}>
                    <div className={styles.worldInfo}>
                      <h3 className={styles.worldName}>{world.name}</h3>
                      {world.description && (
                        <p className={styles.worldDescription}>{world.description}</p>
                      )}

                      <div className={styles.worldMeta}>
                        <div className={styles.metaItem}>
                          <Calendar />
                          <span>Создан: {formatDate(new Date(world.createdAt))}</span>
                        </div>
                        <div className={styles.metaItem}>
                          <Clock />
                          <span>Последняя игра: {formatDate(world.lastPlayed)}</span>
                        </div>
                        <div className={styles.metaItem}>
                          <BarChart3 />
                          <span>Прогресс: {world.progress}%</span>
                        </div>
                      </div>
                    </div>

                    <div className={styles.worldActions}>
                      <button
                        onClick={() => handleLoadWorld(world.id)}
                        className={`${styles.actionButton} ${styles.playButton}`}
                      >
                        <Play />
                        Играть
                      </button>
                      <button
                        onClick={() => handleDeleteWorld(world.id, world.name)}
                        className={`${styles.actionButton} ${styles.deleteButton}`}
                        title="Удалить мир"
                      >
                        <Trash2 />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
          </div>
        )
      )}
    </div>
  )
}

export default WorldList
