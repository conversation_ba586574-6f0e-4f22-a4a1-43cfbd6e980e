import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Settings, Save, Upload, LogOut, AlertTriangle, Volume2, VolumeX } from 'lucide-react'
import { useGameStore } from '../store/gameStore'
import { useAuthStore } from '../store/authStore'
import GameSettings from './comp/GameSettings'
import styles from './GamePage.module.css'

const GamePage: React.FC = () => {
  const navigate = useNavigate()
  const { currentWorldId, worlds, settings, updateSettings } = useGameStore()
  const { logout } = useAuthStore()
  
  const [showSettings, setShowSettings] = useState(false)
  const [showSaveDialog, setShowSaveDialog] = useState(false)
  const [showLoadDialog, setShowLoadDialog] = useState(false)
  const [showExitDialog, setShowExitDialog] = useState(false)
  const [isMuted, setIsMuted] = useState(!settings.soundEnabled)

  // Получаем данные текущего мира
  const currentWorld = worlds.find(world => world.id === currentWorldId)

  useEffect(() => {
    // Если нет выбранного мира, перенаправляем в меню
    if (!currentWorldId) {
      navigate('/menu')
    }
  }, [currentWorldId, navigate])

  const handleSave = () => {
    setShowSaveDialog(true)
  }

  const handleLoad = () => {
    setShowLoadDialog(true)
  }

  const handleExit = () => {
    setShowExitDialog(true)
  }

  const confirmExit = () => {
    navigate('/menu')
  }

  const toggleMute = () => {
    const newMuted = !isMuted
    setIsMuted(newMuted)
    updateSettings({ soundEnabled: !newMuted, musicEnabled: !newMuted })
  }

  if (!currentWorld) {
    return (
      <div className={styles.loading}>
        <div className={styles.loadingContent}>
          <div className={styles.loadingSpinner}></div>
          <p>Загрузка мира...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.gamePage}>
      {/* Игровой интерфейс */}
      <div className={styles.gameInterface}>
        {/* Верхняя панель */}
        <div className={styles.topBar}>
          <div className={styles.worldInfo}>
            <h1 className={styles.worldName}>{currentWorld.name}</h1>
            <span className={styles.worldDescription}>{currentWorld.description}</span>
          </div>
          
          <div className={styles.gameControls}>
            <button 
              className={styles.controlButton}
              onClick={toggleMute}
              title={isMuted ? 'Включить звук' : 'Выключить звук'}
            >
              {isMuted ? <VolumeX /> : <Volume2 />}
            </button>
            
            <button 
              className={styles.controlButton}
              onClick={handleSave}
              title="Сохранить игру"
            >
              <Save />
            </button>
            
            <button 
              className={styles.controlButton}
              onClick={handleLoad}
              title="Загрузить игру"
            >
              <Upload />
            </button>
            
            <button 
              className={styles.controlButton}
              onClick={() => setShowSettings(true)}
              title="Настройки"
            >
              <Settings />
            </button>
            
            <button 
              className={styles.controlButton}
              onClick={handleExit}
              title="Выйти в меню"
            >
              <LogOut />
            </button>
          </div>
        </div>

        {/* Основная игровая область */}
        <div className={styles.gameArea}>
          
        </div>
      </div>

      {/* Диалог настроек */}
      {showSettings && (
        <div className={styles.modal}>
          <div className={styles.modalContent}>
            <GameSettings onClose={() => setShowSettings(false)} />
          </div>
        </div>
      )}

      {/* Диалог сохранения */}
      {showSaveDialog && (
        <div className={styles.modal}>
          <div className={styles.modalContent}>
            <div className={styles.dialogHeader}>
              <h3>Сохранить игру</h3>
            </div>
            <div className={styles.dialogBody}>
              <p>Выберите тип сохранения:</p>
              <div className={styles.saveOptions}>
                <button className={styles.saveButton}>
                  <Save />
                  Новое сохранение
                </button>
                <button className={styles.saveButton}>
                  <AlertTriangle />
                  Перезаписать текущее
                </button>
              </div>
            </div>
            <div className={styles.dialogActions}>
              <button 
                className={styles.cancelButton}
                onClick={() => setShowSaveDialog(false)}
              >
                Отмена
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Диалог загрузки */}
      {showLoadDialog && (
        <div className={styles.modal}>
          <div className={styles.modalContent}>
            <div className={styles.dialogHeader}>
              <h3>Загрузить игру</h3>
            </div>
            <div className={styles.dialogBody}>
              <p>Вы уверены, что хотите загрузить сохранение?</p>
              <p className={styles.warning}>Несохраненный прогресс будет потерян!</p>
            </div>
            <div className={styles.dialogActions}>
              <button className={styles.confirmButton}>
                Да, загрузить
              </button>
              <button 
                className={styles.cancelButton}
                onClick={() => setShowLoadDialog(false)}
              >
                Отмена
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Диалог выхода */}
      {showExitDialog && (
        <div className={styles.modal}>
          <div className={styles.modalContent}>
            <div className={styles.dialogHeader}>
              <h3>Выйти в меню</h3>
            </div>
            <div className={styles.dialogBody}>
              <p>Вы уверены, что хотите выйти в главное меню?</p>
              <p className={styles.warning}>Несохраненный прогресс будет потерян!</p>
            </div>
            <div className={styles.dialogActions}>
              <button 
                className={styles.confirmButton}
                onClick={confirmExit}
              >
                Да, выйти
              </button>
              <button 
                className={styles.cancelButton}
                onClick={() => setShowExitDialog(false)}
              >
                Отмена
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default GamePage
