/* Основные стили игровой страницы с темной постапокалиптической тематикой */

.gamePage {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
  color: #e0e0e0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow: hidden;
  position: relative;
}

.gamePage::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(255, 69, 0, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 140, 0, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(139, 69, 19, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 1;
}

.gameInterface {
  position: relative;
  z-index: 2;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Верхняя панель */
.topBar {
  background: rgba(0, 0, 0, 0.8);
  border-bottom: 2px solid #444;
  padding: 12px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.worldInfo h1 {
  margin: 0;
  font-size: 24px;
  color: #ff6b35;
  text-shadow: 0 0 10px rgba(255, 107, 53, 0.3);
  font-weight: 700;
}

.worldDescription {
  color: #bbb;
  font-size: 14px;
  margin-top: 4px;
}

.gameControls {
  display: flex;
  gap: 8px;
}

.controlButton {
  background: rgba(255, 107, 53, 0.1);
  border: 1px solid #ff6b35;
  color: #ff6b35;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
}

.controlButton:hover {
  background: rgba(255, 107, 53, 0.2);
  box-shadow: 0 0 15px rgba(255, 107, 53, 0.3);
  transform: translateY(-1px);
}

.controlButton:active {
  transform: translateY(0);
}

/* Основная игровая область */
.gameArea {
  flex: 1;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.gameContent {
  width: 100%;
  max-width: 1200px;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid #444;
  border-radius: 12px;
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
}

.placeholder {
  text-align: center;
  padding: 40px;
}

.placeholderIcon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.7;
}

.placeholder h2 {
  color: #ff6b35;
  margin-bottom: 16px;
  font-size: 28px;
  text-shadow: 0 0 10px rgba(255, 107, 53, 0.3);
}

.placeholder p {
  color: #bbb;
  font-size: 16px;
  margin-bottom: 30px;
}

.gameStats {
  display: flex;
  gap: 30px;
  justify-content: center;
  margin-top: 20px;
}

.statItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.statLabel {
  color: #888;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.statValue {
  color: #ff6b35;
  font-size: 18px;
  font-weight: 600;
}

/* Модальные окна */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modalContent {
  background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
  border: 2px solid #444;
  border-radius: 12px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.7);
}

.dialogHeader {
  padding: 20px 20px 0;
  border-bottom: 1px solid #444;
}

.dialogHeader h3 {
  margin: 0 0 20px 0;
  color: #ff6b35;
  font-size: 20px;
  text-shadow: 0 0 10px rgba(255, 107, 53, 0.3);
}

.dialogBody {
  padding: 20px;
}

.dialogBody p {
  margin: 0 0 16px 0;
  color: #ccc;
  line-height: 1.5;
}

.warning {
  color: #ff6b35 !important;
  font-weight: 600;
  text-shadow: 0 0 5px rgba(255, 107, 53, 0.3);
}

.saveOptions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 20px;
}

.saveButton {
  background: rgba(255, 107, 53, 0.1);
  border: 1px solid #ff6b35;
  color: #ff6b35;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.saveButton:hover {
  background: rgba(255, 107, 53, 0.2);
  box-shadow: 0 0 15px rgba(255, 107, 53, 0.3);
}

.dialogActions {
  padding: 0 20px 20px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.confirmButton {
  background: #ff6b35;
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.confirmButton:hover {
  background: #e55a2b;
  box-shadow: 0 0 15px rgba(255, 107, 53, 0.4);
}

.cancelButton {
  background: transparent;
  border: 1px solid #666;
  color: #ccc;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancelButton:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: #888;
}

/* Загрузка */
.loading {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #e0e0e0;
}

.loadingContent {
  text-align: center;
}

.loadingSpinner {
  width: 50px;
  height: 50px;
  border: 3px solid #444;
  border-top: 3px solid #ff6b35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingContent p {
  color: #bbb;
  font-size: 16px;
}
